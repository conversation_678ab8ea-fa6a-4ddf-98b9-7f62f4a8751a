<?xml version="1.0" encoding="utf-8"?>
<XTextDocument xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" EditorVersionString="DCWriter20250619152910">
   <Attributes>
      <Attribute>
         <Name>科室</Name>
         <Value>神经内科总院</Value>
      </Attribute>
      <Attribute>
         <Name>科室Code</Name>
         <Value>200401</Value>
      </Attribute>
      <Attribute>
         <Name>病区</Name>
         <Value>神经内科总院病区护士站</Value>
      </Attribute>
      <Attribute>
         <Name>床号</Name>
         <Value>28</Value>
      </Attribute>
   </Attributes>
   <InnerID>14</InnerID>
   <ContentReadonly>True</ContentReadonly>
   <XElements>
      <Element xsi:type="XTextHeader">
         <InnerID>106</InnerID>
         <AcceptTab>true</AcceptTab>
         <XElements>
            <Element xsi:type="XParagraphFlag" StyleIndex="0">
               <InnerID>111</InnerID>
               <AutoCreate>true</AutoCreate>
            </Element>
         </XElements>
      </Element>
      <Element xsi:type="XTextBody">
         <InnerID>107</InnerID>
         <AcceptTab>true</AcceptTab>
         <XElements>
            <Element xsi:type="XInputField">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>查房日期时间</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001145</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>DT</Value>
                  </Attribute>
                  <Attribute>
                     <Name>ShowTimeFlag</Name>
                     <Value>1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>77</InnerID>
               <ID>field1</ID>
               <ToolTip>【查房日期时间】开始查房时的公元纪年日期和时间的完整描述</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding>
                  <DataSource>TI00000210</DataSource>
                  <BindingPath>CI00002256</BindingPath>
               </ValueBinding>
               <XElements>
                  <Element xsi:type="XString">
                     <InnerID>120</InnerID>
                     <Text>2025年08月03日 17时48分</Text>
                  </Element>
               </XElements>
               <Deleteable>false</Deleteable>
               <Name>FC0000001145</Name>
               <DisplayFormat>
                  <Style>DateTime</Style>
                  <Format>yyyy年MM月dd日 HH时mm分</Format>
               </DisplayFormat>
               <InnerValue>2025/8/3 17:48:43</InnerValue>
               <BackgroundText>yyyy年MM月dd日 HH时mm分</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings>
                  <EditStyle>DateTime</EditStyle>
               </FieldSettings>
            </Element>
            <Element xsi:type="XString" WhitespaceCount="7">
               <InnerID>116</InnerID>
               <Text>       </Text>
            </Element>
            <Element xsi:type="XString" StyleIndex="0" WhitespaceCount="1">
               <InnerID>117</InnerID>
               <Text> </Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="0">

               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>查房医师</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000009718</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignByDoctorFlag</Name>
                     <Value>1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>InputModeParam</Name>
                     <Value>{"DataSourceCode":"WardRoundDoctors","FixedFilter":{"Relation":"OR","Filters":[{"FieldCode":"DeptCode","Operator":"EQ","Parameter":"{patient.deptCode}"}]},"Sort":[{"FieldCode":"StaffFlag"}]}</Value>
                  </Attribute>
               </Attributes>
               <InnerID>99</InnerID>
               <ID>field5</ID>
               <ToolTip>【查房医师】</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding />
               <Deleteable>false</Deleteable>
               <UserEditable>false</UserEditable>
               <Name>FC0000009718</Name>
               <BackgroundText>查房医师</BackgroundText>
               <EditorControlTypeName>EkHis.Components.Emr.Winform.View.AsyncInputControlForDCWriter</EditorControlTypeName>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="0">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>查房记录</Value>
                  </Attribute>
               </Attributes>
               <InnerID>626</InnerID>
               <ID>FC0000001144</ID>
               <Deleteable>false</Deleteable>
               <Name>0</Name>
               <Width>350.8545</Width>
               <Height>49.90234</Height>
               <Text>主治医师查房记录</Text>
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>633</InnerID>
            </Element>
            <Element xsi:type="XString">
               <InnerID>118</InnerID>
               <Text>这里由ai编写</Text>
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>375</InnerID>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="1">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>主治医师签名</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001159</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignLevel</Name>
                     <Value>EMR_AUDIT_ATTEND</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignVerify</Name>
                     <Value>2</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignByDoctorFlag</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>70</InnerID>
               <ID>field4</ID>
               <ToolTip>【主治医师签名】具有主治医师专业技术职务资格的医师签署的在公安户籍管理部门正式登记注册的姓氏和名称</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding />
               <Deleteable>false</Deleteable>
               <UserEditable>false</UserEditable>
               <Name>FC0000001159</Name>
               <BackgroundText>上级医师签名</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XString" StyleIndex="1">
               <InnerID>119</InnerID>
               <Text>/</Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="1">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>记录人签名</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001147</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignLevel</Name>
                     <Value>EMR_AUDIT_RESIDENT</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignVerify</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>1</Value>
                  </Attribute>
               </Attributes>
               <InnerID>62</InnerID>
               <ID>field3</ID>
               <ToolTip>【记录人签名】记录单填写者签署的在公安户籍管理部门正式登记注册的姓氏和名称</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding />
               <Deleteable>false</Deleteable>
               <Name>FC0000001147</Name>
               <BackgroundText>双击签名</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XParagraphFlag" StyleIndex="1">
               <InnerID>69</InnerID>
            </Element>
         </XElements>
      </Element>
      <Element xsi:type="XTextFooter">
         <InnerID>108</InnerID>
         <AcceptTab>true</AcceptTab>
         <XElements>
            <Element xsi:type="XParagraphFlag">
               <InnerID>113</InnerID>
               <AutoCreate>true</AutoCreate>
            </Element>
         </XElements>
      </Element>
   </XElements>
   <Parameters>
      <Parameter Name="TI00000210" />
      <Parameter Name="TI00000206" />
      <Parameter Name="TI00000212" />
   </Parameters>
   <FileName>D:\ekingsoft\doctor5\ekingemr\tempdoc\f087b80bd78541ec85b8cdb51b6aff66.xml</FileName>
   <FileFormat>XML</FileFormat>
   <ContentStyles>
      <Default xsi:type="DocumentContentStyle">
         <FontName>宋体</FontName>
         <FontSize>10.5</FontSize>
      </Default>
      <Styles>
         <Style Index="0">
            <FontName>宋体</FontName>
            <FontSize>10.5</FontSize>
            <Bold>true</Bold>
         </Style>
         <Style Index="1">
            <FontName>宋体</FontName>
            <FontSize>10.5</FontSize>
            <Align>Right</Align>
         </Style>
      </Styles>
   </ContentStyles>
   <Info>
      <Title>08-03 17:48 主治医师查房记录</Title>
      <LicenseText>江苏鑫亿软件股份有限公司:鹤壁市人民医院</LicenseText>
      <CreationTime>1980-01-01T00:00:00</CreationTime>
      <LastModifiedTime>2025-08-03T17:57:27.1801258+08:00</LastModifiedTime>
      <LastPrintTime>1980-01-01T00:00:00</LastPrintTime>
      <Operator>DCSoft.Writer Version:1.2022.6.27</Operator>
      <NumOfPage>1</NumOfPage>
   </Info>
   <BodyText>2025年08月03日 17时48分        查房医师主治医师查房记录
这里由ai编写
上级医师签名/双击签名</BodyText>
   <LocalConfig />
   <PageSettings>
      <PaperKind>Custom</PaperKind>
      <PaperWidth>716</PaperWidth>
      <PaperHeight>1043</PaperHeight>
      <LeftMargin>35</LeftMargin>
      <TopMargin>164</TopMargin>
      <RightMargin>35</RightMargin>
      <BottomMargin>59</BottomMargin>
   </PageSettings>
</XTextDocument>
